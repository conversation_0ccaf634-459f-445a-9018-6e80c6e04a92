import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  users: defineTable({
    tokenIdentifier: v.string(),
    email: v.string(),
    username: v.string(),
    firstName: v.string(),
    lastName: v.string(),
    role: v.union(
      v.literal("client"),
      v.literal("designer"),
      v.literal("admin")
    ),
    createdAt: v.number(),
  }).index("by_token", ["tokenIdentifier"]),

  oneTimeTokens: defineTable({
    token: v.string(),
    role: v.union(v.literal("designer"), v.literal("admin")),
    used: v.boolean(),
    createdAt: v.number(),
    expiresAt: v.number(),
    issuedBy: v.string(), // tokenIdentifier of issuing admin
    usedBy: v.optional(v.string()), // tokenIdentifier of redeemer
    usedAt: v.optional(v.number()),
  }).index("by_token", ["token"]),
});
