import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

// --- Create or update user ---
export const upsertUser = mutation({
  args: {
    tokenIdentifier: v.string(),
    email: v.string(),
    username: v.string(),
    firstName: v.string(),
    lastName: v.string(),
    role: v.union(
      v.literal("client"),
      v.literal("designer"),
      v.literal("admin")
    ),
  },
  handler: async (ctx, args) => {
    const existing = await ctx.db
      .query("users")
      .withIndex("by_token", (q) => q.eq("tokenIdentifier", args.tokenIdentifier))
      .unique();

    if (existing) {
      await ctx.db.patch(existing._id, {
        email: args.email,
        username: args.username,
        firstName: args.firstName,
        lastName: args.lastName,
        role: args.role,
      });
      return existing._id;
    }

    return await ctx.db.insert("users", {
      tokenIdentifier: args.tokenIdentifier,
      email: args.email,
      username: args.username,
      firstName: args.firstName,
      lastName: args.lastName,
      role: args.role,
      createdAt: Date.now(),
    });
  },
});

// --- Get user by tokenIdentifier ---
export const getUserByToken = query({
  args: { tokenIdentifier: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("users")
      .withIndex("by_token", (q) => q.eq("tokenIdentifier", args.tokenIdentifier))
      .unique();
  },
});

// --- List all users (for admin dashboard) ---
export const listUsers = query({
  args: {},
  handler: async (ctx) => {
    return await ctx.db.query("users").collect();
  },
});
