// src/pages/dashboards/AdminDashboard.tsx
import React, { useState } from "react";
import { useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { useUser } from "@clerk/clerk-react";

const AdminDashboard: React.FC = () => {
  const { isSignedIn } = useUser();
  const [role, setRole] = useState<"designer" | "admin">("designer");
  const [token, setToken] = useState<string | null>(null);
  const [expiresAt, setExpiresAt] = useState<number | null>(null);

  // NOTE: this name must match the server export (generateToken)
  const generateToken = useMutation(api.tokens.generateToken);

  const handleGenerate = async () => {
    if (!isSignedIn) {
      alert("You must be signed in as an admin to generate tokens.");
      return;
    }

    try {
      // server returns { token, expiresAt }
      const res = await generateToken({ role });
      setToken(res.token);
      setExpiresAt(res.expiresAt);
    } catch (err: any) {
      console.error("Generate token error:", err);
      alert(err?.message ?? String(err));
    }
  };

  return (
    <div className="p-6">
      <h1 className="text-3xl font-bold">Admin Dashboard</h1>
      <p>Manage users and issue signup tokens for designers/admins.</p>

      <div className="mt-6 space-y-4">
        <label className="block">
          <span className="font-semibold">Select Role:</span>
          <select
            value={role}
            onChange={(e) => setRole(e.target.value as "designer" | "admin")}
            className="border rounded p-2 ml-2"
          >
            <option value="designer">Designer</option>
            <option value="admin">Admin</option>
          </select>
        </label>

        <button
          onClick={handleGenerate}
          className="px-4 py-2 bg-blue-600 text-white rounded shadow hover:bg-blue-700"
        >
          Generate Token
        </button>

        {token && (
          <div className="mt-4 p-4 border rounded bg-gray-100">
            <p className="font-mono">One-time token:</p>
            <p className="font-bold text-lg">{token}</p>
            {expiresAt && (
              <p className="text-sm text-gray-600">
                Expires: {new Date(expiresAt).toLocaleString()}
              </p>
            )}
            <p className="text-sm text-gray-600">
              Share this token with the invited {role}. It is valid for one use
              only.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdminDashboard;
