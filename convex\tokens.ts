// convex/tokens.ts
import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

/** lightweight token generator (no external deps) */
function randomToken(length = 40) {
  const alphabet = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  let s = "";
  for (let i = 0; i < length; i++) {
    s += alphabet[Math.floor(Math.random() * alphabet.length)];
  }
  return s;
}

/**
 * generateToken(role) -> returns { token, expiresAt }
 * - requires the caller to be authenticated and an admin (checked server-side)
 * - optional expiresInMs to override expiry
 */
export const generateToken = mutation({
  args: {
    role: v.union(v.literal("designer"), v.literal("admin")),
    expiresInMs: v.optional(v.number()),
  },
  handler: async (ctx, { role, expiresInMs }) => {
    // Ensure caller is authenticated
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Unauthorized");

    // Make sure the caller is an admin in the users table
    const issuer = await ctx.db
      .query("users")
      .withIndex("by_token", (q) => q.eq("tokenIdentifier", identity.tokenIdentifier))
      .unique();

    if (!issuer || issuer.role !== "admin") {
      throw new Error("Only admins can generate tokens");
    }

    const now = Date.now();
    const token = randomToken(48);
    const expiresAt = now + (expiresInMs ?? 1000 * 60 * 60 * 24); // default 24h

    await ctx.db.insert("oneTimeTokens", {
      token,
      role,
      used: false,
      createdAt: now,
      expiresAt,
      issuedBy: identity.tokenIdentifier,
      usedBy: undefined,
      usedAt: undefined,
    });

    return { token, expiresAt };
  },
});

/** Redeem token: authenticated user redeems token and gets the role assigned */
export const redeemToken = mutation({
  args: { token: v.string() },
  handler: async (ctx, { token }) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Unauthorized");

    const rec = await ctx.db
      .query("oneTimeTokens")
      .withIndex("by_token", (q) => q.eq("token", token))
      .unique();

    if (!rec) throw new Error("Invalid token");
    if (rec.used) throw new Error("Token already used");
    if (rec.expiresAt < Date.now()) throw new Error("Token expired");

    // Upsert or patch the user and set role
    const existing = await ctx.db
      .query("users")
      .withIndex("by_token", (q) => q.eq("tokenIdentifier", identity.tokenIdentifier))
      .unique();

    const patch = {
      email: identity.email ?? "",
      username: identity.nickname ?? "",
      firstName: identity.givenName ?? "",
      lastName: identity.familyName ?? "",
      role: rec.role,
    };

    if (existing) {
      await ctx.db.patch(existing._id, patch);
    } else {
      await ctx.db.insert("users", {
        tokenIdentifier: identity.tokenIdentifier,
        createdAt: Date.now(),
        ...patch,
      });
    }

    // mark token used
    await ctx.db.patch(rec._id, {
      used: true,
      usedBy: identity.tokenIdentifier,
      usedAt: Date.now(),
    });

    return { ok: true, role: rec.role };
  },
});

/** list tokens issued by the signed-in admin (or all tokens if you prefer) */
export const listTokens = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Unauthorized");

    const me = await ctx.db
      .query("users")
      .withIndex("by_token", (q) => q.eq("tokenIdentifier", identity.tokenIdentifier))
      .unique();

    if (!me || me.role !== "admin") {
      throw new Error("Only admins can list tokens");
    }

    // return tokens issued by this admin
    return await ctx.db
      .query("oneTimeTokens")
      .filter((q) => q.eq(q.field("issuedBy"), identity.tokenIdentifier))
      .collect();
  },
});
